<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API集成测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        .product-card { margin: 10px 0; padding: 10px; border: 1px solid #eee; }
        .product-image { max-width: 150px; max-height: 150px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🚀 生产模式API集成测试</h1>
    
    <div id="config-test" class="test-section loading">
        <h3>📋 1. 环境配置检查</h3>
        <div id="config-result">检查中...</div>
    </div>
    
    <div id="backend-test" class="test-section loading">
        <h3>🔧 2. 后端API连接测试</h3>
        <div id="backend-result">测试中...</div>
    </div>
    
    <div id="products-test" class="test-section loading">
        <h3>📦 3. 产品数据获取测试</h3>
        <div id="products-result">获取中...</div>
    </div>
    
    <div id="images-test" class="test-section loading">
        <h3>🖼️ 4. 图片资源访问测试</h3>
        <div id="images-result">测试中...</div>
    </div>

    <script>
        // 配置信息
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        const IMAGE_BASE_URL = 'http://*************:9000';
        
        // 工具函数
        function updateSection(sectionId, content, status = 'success') {
            const section = document.getElementById(sectionId);
            const result = document.getElementById(sectionId.replace('-test', '-result'));
            
            section.className = `test-section ${status}`;
            result.innerHTML = content;
        }
        
        function formatJson(obj) {
            return `<pre>${JSON.stringify(obj, null, 2)}</pre>`;
        }
        
        // 1. 环境配置检查
        function checkConfig() {
            const config = {
                apiBaseUrl: API_BASE_URL,
                imageBaseUrl: IMAGE_BASE_URL,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
            
            updateSection('config-test', 
                `<p>✅ 配置信息加载成功</p>${formatJson(config)}`, 
                'success'
            );
        }
        
        // 2. 后端API连接测试
        async function testBackendConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/../health`);
                const data = await response.json();
                
                if (data.status === 'ok') {
                    updateSection('backend-test', 
                        `<p>✅ 后端服务连接成功</p>
                         <p>MongoDB状态: ${data.mongodb}</p>
                         <p>运行时间: ${Math.round(data.uptime)}秒</p>
                         ${formatJson(data)}`, 
                        'success'
                    );
                } else {
                    throw new Error('健康检查失败');
                }
            } catch (error) {
                updateSection('backend-test', 
                    `<p>❌ 后端服务连接失败</p>
                     <p>错误: ${error.message}</p>`, 
                    'error'
                );
            }
        }
        
        // 3. 产品数据获取测试
        async function testProductsAPI() {
            try {
                const response = await fetch(`${API_BASE_URL}/products?page=1&limit=3`);
                const data = await response.json();
                
                if (data.success && data.data.products.length > 0) {
                    const products = data.data.products;
                    const productList = products.map(p => 
                        `<div class="product-card">
                            <strong>${p.name}</strong><br>
                            ID: ${p.productId}<br>
                            价格: ¥${p.price.normal}<br>
                            分类: ${p.category.primary} > ${p.category.secondary}<br>
                            图片数量: ${Object.keys(p.images || {}).length}
                        </div>`
                    ).join('');
                    
                    updateSection('products-test', 
                        `<p>✅ 产品数据获取成功</p>
                         <p>获取到 ${products.length} 个产品</p>
                         ${productList}
                         <details>
                            <summary>查看原始数据</summary>
                            ${formatJson(data)}
                         </details>`, 
                        'success'
                    );
                    
                    // 传递产品数据给图片测试
                    window.testProducts = products;
                } else {
                    throw new Error('产品数据格式错误');
                }
            } catch (error) {
                updateSection('products-test', 
                    `<p>❌ 产品数据获取失败</p>
                     <p>错误: ${error.message}</p>`, 
                    'error'
                );
            }
        }
        
        // 4. 图片资源访问测试
        async function testImagesAccess() {
            try {
                if (!window.testProducts || window.testProducts.length === 0) {
                    throw new Error('没有产品数据用于测试');
                }
                
                const product = window.testProducts[0];
                const images = product.images || {};
                
                if (Object.keys(images).length === 0) {
                    throw new Error('产品没有图片数据');
                }
                
                let imageTests = [];
                let successCount = 0;
                
                for (const [type, url] of Object.entries(images)) {
                    if (url) {
                        try {
                            // 测试图片是否可访问
                            const img = new Image();
                            const loadPromise = new Promise((resolve, reject) => {
                                img.onload = () => resolve(true);
                                img.onerror = () => reject(false);
                                setTimeout(() => reject(false), 5000); // 5秒超时
                            });
                            
                            img.src = url;
                            const loaded = await loadPromise;
                            
                            if (loaded) {
                                successCount++;
                                imageTests.push(`
                                    <div class="product-card">
                                        <strong>${type}图片</strong> ✅<br>
                                        <img src="${url}" class="product-image" alt="${type}"><br>
                                        <small>${url}</small>
                                    </div>
                                `);
                            } else {
                                imageTests.push(`
                                    <div class="product-card">
                                        <strong>${type}图片</strong> ❌<br>
                                        <small>${url}</small><br>
                                        <span style="color: red;">加载失败</span>
                                    </div>
                                `);
                            }
                        } catch (error) {
                            imageTests.push(`
                                <div class="product-card">
                                    <strong>${type}图片</strong> ❌<br>
                                    <small>${url}</small><br>
                                    <span style="color: red;">错误: ${error}</span>
                                </div>
                            `);
                        }
                    }
                }
                
                const totalImages = Object.keys(images).length;
                const status = successCount === totalImages ? 'success' : 
                              successCount > 0 ? 'loading' : 'error';
                
                updateSection('images-test', 
                    `<p>${successCount === totalImages ? '✅' : '⚠️'} 图片访问测试完成</p>
                     <p>成功: ${successCount}/${totalImages}</p>
                     <p>产品: ${product.name}</p>
                     ${imageTests.join('')}`, 
                    status
                );
                
            } catch (error) {
                updateSection('images-test', 
                    `<p>❌ 图片访问测试失败</p>
                     <p>错误: ${error.message}</p>`, 
                    'error'
                );
            }
        }
        
        // 执行所有测试
        async function runAllTests() {
            console.log('🚀 开始API集成测试...');
            
            // 1. 配置检查
            checkConfig();
            
            // 2. 后端连接测试
            await testBackendConnection();
            
            // 3. 产品数据测试
            await testProductsAPI();
            
            // 4. 图片访问测试
            await testImagesAccess();
            
            console.log('✅ 所有测试完成');
        }
        
        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', runAllTests);
    </script>
</body>
</html>
