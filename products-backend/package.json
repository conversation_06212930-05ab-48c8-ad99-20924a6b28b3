{"name": "products-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "nodemon --exec ts-node src/app.ts", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/multipart": "^9.0.3", "@fastify/rate-limit": "^10.3.0", "@types/axios": "^0.9.36", "@types/node-cron": "^3.0.11", "axios": "^1.10.0", "dotenv": "^17.2.0", "fastify": "^5.4.0", "minio": "^8.0.5", "mongoose": "^8.16.4", "node-cron": "^4.2.1", "redis": "^5.6.0", "sharp": "^0.34.3", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^24.0.14", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}