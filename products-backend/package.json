{
  "name": "products-backend",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "scripts": {
    "build": "tsc",
    "start": "node dist/app.js",
    "dev": "nodemon --exec ts-node src/app.ts",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "type": "commonjs",
  "dependencies": {
    "@fastify/cors": "^11.0.1",
    "@fastify/multipart": "^9.0.3",
    "@fastify/rate-limit": "^10.3.0",
<<<<<<< HEAD
    "@types/axios": "^0.9.36",
    "@types/node-cron": "^3.0.11",
=======
    "@fastify/websocket": "^11.2.0",
    "@types/node-cron": "^3.0.11",
    "@types/uuid": "^10.0.0",
    "@types/ws": "^8.18.1",
>>>>>>> backup-latest
    "axios": "^1.10.0",
    "dotenv": "^17.2.0",
    "fastify": "^5.4.0",
    "minio": "^8.0.5",
    "mongoose": "^8.16.4",
    "node-cron": "^4.2.1",
    "redis": "^5.6.0",
    "sharp": "^0.34.3",
    "uuid": "^11.1.0",
    "winston": "^3.17.0",
    "ws": "^8.18.3"
  },
  "devDependencies": {
    "@types/jest": "^30.0.0",
    "@types/node": "^24.0.14",
    "@typescript-eslint/eslint-plugin": "^8.37.0",
    "@typescript-eslint/parser": "^8.37.0",
    "eslint": "^9.31.0",
    "jest": "^30.0.4",
    "nodemon": "^3.1.10",
    "prettier": "^3.6.2",
    "ts-jest": "^29.4.0",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3"
  }
}
