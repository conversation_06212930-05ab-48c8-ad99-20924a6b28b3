/**
 * 飞书API连接测试脚本
 * 验证飞书API配置是否正确
 */

import { feishuService } from '../services/feishuService';
import { validateFeishuConfig } from '../config/feishuConfig';

export async function testFeishuConnection() {
  console.log('🔍 开始测试飞书API连接...\n');

  // 1. 验证配置
  console.log('1. 验证配置信息...');
  const configValidation = validateFeishuConfig();
  
  if (!configValidation.valid) {
    console.error('❌ 配置验证失败:');
    configValidation.missing.forEach(field => {
      console.error(`   - 缺少: ${field}`);
    });
    return;
  }
  
  console.log('✅ 配置验证通过');
  console.log('   配置状态:', configValidation.status);
  console.log('');

  // 2. 测试连接
  console.log('2. 测试飞书API连接...');
  try {
    const connectionResult = await feishuService.testConnection();
    
    if (connectionResult.success) {
      console.log('✅ 飞书API连接成功!');
      console.log('   连接信息:', connectionResult.message);
      if (connectionResult.details) {
        console.log('   应用信息:', connectionResult.details.app);
        console.log('   表格信息:', connectionResult.details.table);
      }
    } else {
      console.error('❌ 飞书API连接失败:', connectionResult.message);
      return;
    }
  } catch (error) {
    console.error('❌ 连接测试异常:', error);
    return;
  }
  console.log('');

  // 3. 测试数据获取
  console.log('3. 测试数据获取...');
  try {
    const records = await feishuService.listRecords({ pageSize: 5 });
    
    console.log(`✅ 成功获取数据!`);
    console.log(`   总记录数: ${records.total}`);
    console.log(`   本次获取: ${records.items.length} 条`);
    console.log(`   是否有更多数据: ${records.has_more}`);
    
    if (records.items.length > 0) {
      console.log('   示例记录:');
      const firstRecord = records.items[0];
      console.log(`   - 记录ID: ${firstRecord.record_id}`);
      console.log(`   - 字段数量: ${Object.keys(firstRecord.fields).length}`);
      console.log(`   - 创建时间: ${new Date(firstRecord.created_time).toLocaleString()}`);
      console.log(`   - 修改时间: ${new Date(firstRecord.last_modified_time).toLocaleString()}`);
    }
  } catch (error) {
    console.error('❌ 数据获取失败:', error);
    return;
  }
  console.log('');

  // 4. 测试数据转换
  console.log('4. 测试数据转换...');
  try {
    const records = await feishuService.listRecords({ pageSize: 3 });
    const products = feishuService.transformRecordsToProducts(records.items);
    
    console.log(`✅ 数据转换成功!`);
    console.log(`   转换记录数: ${products.length}`);
    
    if (products.length > 0) {
      console.log('   示例产品:');
      const firstProduct = products[0];
      console.log(`   - 产品ID: ${firstProduct.id}`);
      console.log(`   - 产品名称: ${firstProduct.name}`);
      console.log(`   - 分类: ${firstProduct.category?.primary}/${firstProduct.category?.secondary}`);
      console.log(`   - 价格: ${firstProduct.price?.normal}`);
      console.log(`   - 平台: ${firstProduct.platform}`);
    }
  } catch (error) {
    console.error('❌ 数据转换失败:', error);
    return;
  }
  console.log('');

  console.log('🎉 所有测试通过! 飞书API配置正确，可以正常使用。');
}

// 运行测试
if (require.main === module) {
  testFeishuConnection()
    .then(() => {
      console.log('\n✨ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试失败:', error);
      process.exit(1);
    });
}