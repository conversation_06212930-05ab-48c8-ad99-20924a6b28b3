/**
 * 飞书API配置文件
 * 处理飞书多维表格API的配置和工具函数
 */

// 飞书API配置
export const FEISHU_CONFIG = {
  APP: {
    APP_ID: process.env.FEISHU_APP_ID || '',
    APP_SECRET: process.env.FEISHU_APP_SECRET || '',
    APP_TOKEN: process.env.FEISHU_APP_TOKEN || '',
    TABLE_ID: process.env.FEISHU_TABLE_ID || '',
    BASE_URL: process.env.FEISHU_BASE_URL || 'https://open.feishu.cn/open-apis/'
  },
  REQUEST: {
    TIMEOUT: parseInt(process.env.FEISHU_REQUEST_TIMEOUT || '10000'),
    RETRY_ATTEMPTS: parseInt(process.env.FEISHU_RETRY_ATTEMPTS || '3'),
    RETRY_DELAY: parseInt(process.env.FEISHU_RETRY_DELAY || '1000')
  },
  ENDPOINTS: {
    // 获取tenant_access_token
    TENANT_ACCESS_TOKEN: '/auth/v3/tenant_access_token/internal',
    // 多维表格相关API
    BITABLE: {
      GET_APP: '/bitable/v1/apps/{app_token}',
      GET_TABLE: '/bitable/v1/apps/{app_token}/tables/{table_id}',
      LIST_RECORDS: '/bitable/v1/apps/{app_token}/tables/{table_id}/records',
      GET_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}',
      CREATE_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records',
      UPDATE_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}',
      DELETE_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}'
    }
  }
};

// 配置验证
export const FEISHU_CONFIG_VALIDATION = {
  get isValid() {
    return this.missingFields.length === 0;
  },
  get missingFields() {
    const missing: string[] = [];
    if (!FEISHU_CONFIG.APP.APP_ID) missing.push('FEISHU_APP_ID');
    if (!FEISHU_CONFIG.APP.APP_SECRET) missing.push('FEISHU_APP_SECRET');
    if (!FEISHU_CONFIG.APP.APP_TOKEN) missing.push('FEISHU_APP_TOKEN');
    if (!FEISHU_CONFIG.APP.TABLE_ID) missing.push('FEISHU_TABLE_ID');
    return missing;
  },
  get configStatus() {
    return {
      APP_ID: !!FEISHU_CONFIG.APP.APP_ID,
      APP_SECRET: !!FEISHU_CONFIG.APP.APP_SECRET,
      APP_TOKEN: !!FEISHU_CONFIG.APP.APP_TOKEN,
      TABLE_ID: !!FEISHU_CONFIG.APP.TABLE_ID,
      BASE_URL: !!FEISHU_CONFIG.APP.BASE_URL
    };
  }
};

// 飞书API工具类
export class FeishuApiUtils {
  /**
   * 构建API URL，替换路径参数
   */
  static buildUrl(endpoint: string, params: Record<string, string> = {}): string {
    let url = endpoint;
    // 替换路径参数
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`{${key}}`, value);
    });
    return url;
  }

  /**
   * 处理飞书API响应
   */
  static handleApiResponse(response: any) {
    if (response.code === 0) {
      return {
        success: true,
        data: response.data
      };
    } else {
      return {
        success: false,
        error: response.msg || '未知错误'
      };
    }
  }

  /**
   * 格式化错误信息
   */
  static formatError(error: any): string {
    if (error.response) {
      // HTTP错误
      const status = error.response.status;
      const data = error.response.data;
      if (data && data.msg) {
        return `飞书API错误 (${status}): ${data.msg}`;
      }
      return `HTTP错误 (${status}): ${error.response.statusText}`;
    } else if (error.request) {
      // 网络错误
      return '网络连接错误，请检查网络设置';
    } else {
      // 其他错误
      return error.message || '未知错误';
    }
  }

  /**
   * 映射飞书字段到系统字段 - 支持新的31个字段
   */
  static mapFeishuFields(fields: any) {
    // 提取图片URL的辅助函数
    const extractImageUrl = (imageField: any): string => {
      if (Array.isArray(imageField) && imageField.length > 0) {
        const image = imageField[0];
        if (image.url) return image.url;
        if (image.file_token) return `https://drive.feishu.cn/file/${image.file_token}`;
      }
      return '';
    };

    // 提取数组中的文本值
    const extractArrayValue = (field: any): string => {
      if (Array.isArray(field) && field.length > 0) {
        const item = field[0];
        if (typeof item === 'string') return item;
        if (item.text) return item.text;
        if (item.name) return item.name;
      }
      if (typeof field === 'string') return field;
      return '';
    };

    // 基础字段映射
    const productId = fields['rx编号'] || fields['编号'] || fields['bar code(条码)'] || `feishu_${Date.now()}`;
    const productName = fields['Product Name'] || fields['品名'] || '';
    
    return {
      // 基础标识信息
      id: productId,
      name: productName,
      sequence: extractArrayValue(fields['序号']) || extractArrayValue(fields['序号1']) || '',
      
      // 分类信息 - 支持中英文
      category: {
        primary: fields['品类一级'] || fields['Category Level 1'] || '未分类',
        secondary: fields['品类二级'] || fields['Category Level 2'] || extractArrayValue(fields['Category Level 2']) || ''
      },
      
      // 价格信息 - 支持多币种
      price: {
        normal: this.parsePrice(fields['正常售价']) || this.parsePrice(fields['Price（USD）']) || 0,
        discount: this.parsePrice(fields['优惠到手价']) || this.parsePrice(fields['Special Price（USD）']) || 0,
        discountRate: 0, // 需要计算
        usd: this.parsePrice(fields['Price（USD）']),
        specialUsd: this.parsePrice(fields['Special Price（USD）'])
      },
      
      // 产地信息
      origin: {
        country: fields['Origin (Country)'] || '中国',
        province: extractArrayValue(fields['Origin (Province)']),
        city: extractArrayValue(fields['Origin (City)'])
      },
      
      // 图片信息 - 支持多种图片类型
      images: {
        front: extractImageUrl(fields['Front image(正)']),
        back: extractImageUrl(fields['Back image(背)']),
        label: extractImageUrl(fields['Tag photo(标签)']),
        package: '',
        gift: ''
      },
      
      // 其他基础信息
      platform: fields['采集平台'] || 'Feishu',
      specification: fields['Specs(规格)'] || '',
      flavor: fields['Flavor(口味)'] || fields['口味'] || '',
      manufacturer: fields['Manufacturer(生产商)'] || '',
      
      // 时间信息
      collectTime: this.parseDate(fields['采集时间']),
      
      // 飞书特有数据
      feishuData: {
        client: fields['Client(委托方)'],
        barcode: fields['bar code(条码)'],
        rxNumber: fields['rx编号'],
        productBrandName: fields['产品品名'],
        discountedPrice: fields['优惠到手价'],
        flavorChinese: fields['口味'],
        categoryLevel1: fields['品类一级'],
        categoryLevel2: fields['品类二级'],
        sequenceDetails: {
          full: fields['序号'],
          part1: fields['序号1'],
          part2: fields['序号2'],
          part3: fields['序号3']
        },
        normalPrice: fields['正常售价'],
        number: fields['编号'],
        collectPlatform: fields['采集平台'],
        collectTimestamp: fields['采集时间'],
        singleMixed: fields['Single/Mixed'],
        // 存储所有原始字段数据
        rawFields: fields
      },
      
      // 动态字段存储所有其他字段
      dynamicFields: this.extractDynamicFields(fields)
    };
  }

  /**
   * 提取动态字段
   */
  static extractDynamicFields(fields: any) {
    const dynamicFields: any = {};
    const knownFields = new Set([
      'rx编号', '编号', 'bar code(条码)', 'Product Name', '品名', '序号', '序号1', '序号2', '序号3',
      '品类一级', '品类二级', 'Category Level 1', 'Category Level 2', '正常售价', '优惠到手价',
      'Price（USD）', 'Special Price（USD）', 'Origin (Country)', 'Origin (Province)', 'Origin (City)',
      'Front image(正)', 'Back image(背)', 'Tag photo(标签)', '采集平台', 'Specs(规格)',
      'Flavor(口味)', '口味', 'Manufacturer(生产商)', '采集时间', 'Client(委托方)', '产品品名',
      'Single/Mixed'
    ]);

    Object.keys(fields).forEach(key => {
      if (!knownFields.has(key)) {
        dynamicFields[key] = fields[key];
      }
    });

    return dynamicFields;
  }

  /**
   * 映射图片字段
   */
  static mapImageFields(fields: any) {
    const images: any = {};
    
    // 处理飞书图片字段
    const imageFields = {
      front: fields['正面图片'] || fields['front_image'],
      back: fields['背面图片'] || fields['back_image'],
      label: fields['标签照片'] || fields['label_image'],
      package: fields['外包装图片'] || fields['package_image'],
      gift: fields['赠品图片'] || fields['gift_image']
    };

    Object.entries(imageFields).forEach(([key, value]) => {
      if (value) {
        // 如果是飞书图片对象，提取URL
        if (Array.isArray(value) && value.length > 0 && value[0].url) {
          images[key] = value[0].url;
        } else if (typeof value === 'string') {
          images[key] = value;
        }
      }
    });

    return images;
  }

  /**
   * 解析价格
   */
  static parsePrice(value: any): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value.replace(/[^\d.]/g, ''));
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  /**
   * 解析数字
   */
  static parseNumber(value: any): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  /**
   * 解析日期
   */
  static parseDate(value: any): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const date = new Date(value);
      return isNaN(date.getTime()) ? Date.now() : date.getTime();
    }
    return Date.now();
  }

  /**
   * 验证飞书记录数据
   */
  static validateRecord(record: any) {
    const errors: string[] = [];
    
    if (!record.id) {
      errors.push('缺少产品ID');
    }
    if (!record.name) {
      errors.push('缺少产品名称');
    }
    if (!record.category?.primary) {
      errors.push('缺少一级分类');
    }
    if (!record.price?.normal || record.price.normal <= 0) {
      errors.push('价格信息无效');
    }
    if (!record.platform) {
      errors.push('缺少平台信息');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 生成同步报告
   */
  static generateSyncReport(data: any): string {
    const duration = (data.endTime - data.startTime) / 1000;
    
    return `
飞书数据同步报告
==========================================
开始时间: ${new Date(data.startTime).toLocaleString()}
结束时间: ${new Date(data.endTime).toLocaleString()}
同步耗时: ${duration}秒

数据统计:
- 总记录数: ${data.total}
- 成功处理: ${data.success}
- 失败记录: ${data.failed}
- 成功率: ${((data.success / data.total) * 100).toFixed(2)}%

${data.errors.length > 0 ? `
错误信息:
${data.errors.map((error: string, index: number) => `${index + 1}. ${error}`).join('\n')}
` : ''}
==========================================
    `.trim();
  }
}

// 飞书字段映射配置
export const FEISHU_FIELD_MAPPING = {
  // 基础字段映射
  BASIC_FIELDS: {
    'id': '产品ID',
    'name': '产品名称',
    'sequence': '序号',
    'platform': '平台',
    'specification': '规格',
    'flavor': '口味',
    'manufacturer': '生产商'
  },
  
  // 分类字段映射
  CATEGORY_FIELDS: {
    'category_primary': '一级分类',
    'category_secondary': '二级分类'
  },
  
  // 价格字段映射
  PRICE_FIELDS: {
    'price_normal': '正价',
    'price_discount': '折扣价',
    'discount_rate': '折扣率'
  },
  
  // 产地字段映射
  ORIGIN_FIELDS: {
    'country': '国家',
    'province': '省份',
    'city': '城市'
  },
  
  // 图片字段映射
  IMAGE_FIELDS: {
    'front_image': '正面图片',
    'back_image': '背面图片',
    'label_image': '标签照片',
    'package_image': '外包装图片',
    'gift_image': '赠品图片'
  },
  
  // 时间字段映射
  TIME_FIELDS: {
    'collect_time': '采集时间'
  }
};

// 导出配置验证函数
export function validateFeishuConfig() {
  return {
    valid: FEISHU_CONFIG_VALIDATION.isValid,
    missing: FEISHU_CONFIG_VALIDATION.missingFields,
    status: FEISHU_CONFIG_VALIDATION.configStatus
  };
}

// 默认导出配置对象
export default {
  FEISHU_CONFIG,
  FEISHU_CONFIG_VALIDATION,
  FeishuApiUtils,
  FEISHU_FIELD_MAPPING,
  validateFeishuConfig
};