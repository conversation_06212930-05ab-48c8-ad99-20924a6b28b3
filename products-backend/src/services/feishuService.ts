/**
 * 飞书API服务
 * 处理与飞书多维表格的数据交互
 */

import axios from 'axios';
import { FEISHU_CONFIG, FEISHU_CONFIG_VALIDATION, FeishuApiUtils } from '../config/feishuConfig';

class FeishuService {
  private tenantAccessToken: string | null = null;
  private tokenExpireTime: number = 0;
  private axiosInstance: any; // 使用any类型避免类型错误

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: FEISHU_CONFIG.APP.BASE_URL,
      timeout: FEISHU_CONFIG.REQUEST.TIMEOUT,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    });

    // 请求拦截器 - 自动添加认证头
    this.axiosInstance.interceptors.request.use(async (config: any) => {
      // 确保有有效的token
      await this.ensureValidToken();
      if (this.tenantAccessToken) {
        config.headers.Authorization = `Bearer ${this.tenantAccessToken}`;
      }
      return config;
    });

    // 响应拦截器 - 处理错误和重试
    this.axiosInstance.interceptors.response.use(
      (response: any) => response,
      async (error: any) => {
        // 如果是token过期，尝试刷新token并重试
        if (error.response?.status === 401) {
          this.tenantAccessToken = null;
          this.tokenExpireTime = 0;
          
          // 重新获取token并重试请求
          await this.ensureValidToken();
          if (this.tenantAccessToken) {
            error.config.headers.Authorization = `Bearer ${this.tenantAccessToken}`;
            return this.axiosInstance.request(error.config);
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * 确保有有效的tenant_access_token
   */
  private async ensureValidToken(): Promise<void> {
    const now = Date.now();
    
    // 如果token还有效（提前5分钟刷新）
    if (this.tenantAccessToken && this.tokenExpireTime > now + 5 * 60 * 1000) {
      return;
    }

    try {
      const response = await axios.post(
        `${FEISHU_CONFIG.APP.BASE_URL}${FEISHU_CONFIG.ENDPOINTS.TENANT_ACCESS_TOKEN}`,
        {
          app_id: FEISHU_CONFIG.APP.APP_ID,
          app_secret: FEISHU_CONFIG.APP.APP_SECRET
        }
      );

      if ((response.data as any).code === 0) {
        this.tenantAccessToken = (response.data as any).tenant_access_token;
        this.tokenExpireTime = now + (response.data as any).expire * 1000;
        console.log('飞书token获取成功');
      } else {
        throw new Error(`获取飞书token失败: ${(response.data as any).msg}`);
      }
    } catch (error) {
      console.error('获取飞书token失败:', error);
      throw error;
    }
  }

  /**
   * 检查服务是否可用
   */
  isAvailable(): boolean {
    return FEISHU_CONFIG_VALIDATION.isValid;
  }

  /**
   * 获取多维表格信息
   */
  async getAppInfo() {
    if (!this.isAvailable()) {
      throw new Error('飞书配置不完整，无法调用API');
    }

    try {
      const url = FeishuApiUtils.buildUrl(FEISHU_CONFIG.ENDPOINTS.BITABLE.GET_APP, {
        app_token: FEISHU_CONFIG.APP.APP_TOKEN
      });

      const response = await this.axiosInstance.get(url);
      return FeishuApiUtils.handleApiResponse(response.data);
    } catch (error) {
      throw new Error(FeishuApiUtils.formatError(error));
    }
  }

  /**
   * 获取数据表信息
   */
  async getTableInfo() {
    if (!this.isAvailable()) {
      throw new Error('飞书配置不完整，无法调用API');
    }

    try {
      const url = FeishuApiUtils.buildUrl(FEISHU_CONFIG.ENDPOINTS.BITABLE.GET_TABLE, {
        app_token: FEISHU_CONFIG.APP.APP_TOKEN,
        table_id: FEISHU_CONFIG.APP.TABLE_ID
      });

      const response = await this.axiosInstance.get(url);
      return FeishuApiUtils.handleApiResponse(response.data);
    } catch (error) {
      throw new Error(FeishuApiUtils.formatError(error));
    }
  }

  /**
   * 获取记录列表
   */
  async listRecords(options: any = {}) {
    if (!this.isAvailable()) {
      throw new Error('飞书配置不完整，无法调用API');
    }

    try {
      const url = FeishuApiUtils.buildUrl(FEISHU_CONFIG.ENDPOINTS.BITABLE.LIST_RECORDS, {
        app_token: FEISHU_CONFIG.APP.APP_TOKEN,
        table_id: FEISHU_CONFIG.APP.TABLE_ID
      });

      const params: any = {
        page_size: options.pageSize || 100
      };

      if (options.pageToken) {
        params.page_token = options.pageToken;
      }
      if (options.filter) {
        params.filter = options.filter;
      }
      if (options.sort && options.sort.length > 0) {
        params.sort = JSON.stringify(options.sort);
      }

      const response = await this.axiosInstance.get(url, { params });
      const result = FeishuApiUtils.handleApiResponse(response.data);
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      throw new Error(FeishuApiUtils.formatError(error));
    }
  }

  /**
   * 获取所有记录（自动分页）- 修复数据获取问题
   */
  async getAllRecords(options: any = {}): Promise<any[]> {
    const allRecords: any[] = [];
    let pageToken: string | undefined;
    let hasMore = true;
    let pageCount = 0;

    console.log('开始获取飞书数据，使用分页获取...');

    while (hasMore) {
      pageCount++;
      console.log(`正在获取第 ${pageCount} 页数据...`);

      try {
        const response = await this.listRecords({
          ...options,
          pageToken,
          pageSize: 500 // 使用最大页面大小
        });

        console.log(`第 ${pageCount} 页获取到 ${response.items.length} 条记录`);
        
        allRecords.push(...response.items);
        hasMore = response.has_more;
        pageToken = response.page_token;

        // 避免请求过于频繁
        if (hasMore) {
          console.log('等待200ms后获取下一页...');
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      } catch (error) {
        console.error(`获取第 ${pageCount} 页数据失败:`, error);
        break;
      }
    }

    console.log(`飞书数据获取完成，总共获取到 ${allRecords.length} 条记录`);
    return allRecords;
  }

  /**
   * 将飞书记录转换为系统产品数据 - 使用新的字段映射
   */
  transformRecordsToProducts(records: any[]): any[] {
    console.log(`开始转换 ${records.length} 条飞书记录为产品数据...`);
    
    const products = records.map((record, index) => {
      try {
        // 使用更新的字段映射转换数据
        const mappedData = FeishuApiUtils.mapFeishuFields(record.fields);
        
        // 添加飞书特有的元数据
        const product = {
          ...mappedData,
          recordId: record.record_id,
          feishuCreatedTime: record.created_time ? new Date(record.created_time) : new Date(),
          feishuModifiedTime: record.last_modified_time ? new Date(record.last_modified_time) : new Date(),
          // 如果没有collectTime，使用飞书的修改时间
          collectTime: mappedData.collectTime || (record.last_modified_time ? new Date(record.last_modified_time) : new Date())
        };

        // 确保有必需的字段
        if (!product.id) {
          product.id = `feishu_record_${record.record_id}`;
        }
        if (!product.name) {
          product.name = `未命名产品_${index + 1}`;
        }

        return product;
      } catch (error) {
        console.error(`转换第 ${index + 1} 条记录失败:`, error);
        // 返回一个基础的产品对象，避免整个转换失败
        return {
          id: `feishu_record_${record.record_id}`,
          recordId: record.record_id,
          name: `数据转换失败_${index + 1}`,
          sequence: '',
          category: { primary: '数据错误', secondary: '' },
          price: { normal: 0, discount: 0, discountRate: 0 },
          images: {},
          origin: { country: '中国', province: '', city: '' },
          platform: 'Feishu',
          specification: '',
          flavor: '',
          manufacturer: '',
          collectTime: new Date(),
          feishuData: { rawFields: record.fields },
          dynamicFields: {}
        };
      }
    });

    console.log(`飞书记录转换完成，成功转换 ${products.length} 条产品数据`);
    return products;
  }

  /**
   * 测试连接
   */
  async testConnection() {
    try {
      if (!this.isAvailable()) {
        return {
          success: false,
          message: '飞书配置不完整: ' + FEISHU_CONFIG_VALIDATION.missingFields.join(', ')
        };
      }

      // 测试获取应用信息
      const appInfo = await this.getAppInfo();
      if (!appInfo.success) {
        return {
          success: false,
          message: '无法获取飞书应用信息: ' + appInfo.error
        };
      }

      // 测试获取表格信息
      const tableInfo = await this.getTableInfo();
      if (!tableInfo.success) {
        return {
          success: false,
          message: '无法获取飞书表格信息: ' + tableInfo.error
        };
      }

      return {
        success: true,
        message: '飞书连接测试成功',
        details: {
          app: appInfo.data,
          table: tableInfo.data
        }
      };
    } catch (error) {
      return {
        success: false,
        message: FeishuApiUtils.formatError(error)
      };
    }
  }
}

// 创建单例实例
export const feishuService = new FeishuService();