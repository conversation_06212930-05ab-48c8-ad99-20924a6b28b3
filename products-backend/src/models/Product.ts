import mongoose, { Schema, Document } from 'mongoose';

export interface IProduct extends Document {
  productId: string;
  recordId: string;
  name: string;
  sequence: string;
  category: {
    primary: string;
    secondary?: string;
  };
  price: {
    normal: number;
    discount: number;
    discountRate: number;
    currency: string;
    usd?: number; // 美元价格
    specialUsd?: number; // 特价美元价格
  };
  images: {
    front?: string;
    back?: string;
    label?: string;
    package?: string;
    gift?: string;
  };
  origin: {
    country: string;
    province: string;
    city: string;
  };
  platform: string;
  specification: string;
  flavor: string;
  manufacturer: string;
  collectTime: Date;
  createdAt: Date;
  updatedAt: Date;
  searchText: string;
  status: string;
  isVisible: boolean;
  
  // 新增字段支持飞书数据
  feishuData?: {
    client?: string; // 委托方
    barcode?: string; // 条码
    rxNumber?: string; // rx编号
    productBrandName?: any; // 产品品名
    discountedPrice?: string; // 优惠到手价
    flavorChinese?: string; // 中文口味
    categoryLevel1?: string; // 品类一级
    categoryLevel2?: string; // 品类二级
    sequenceDetails?: {
      full?: any; // 完整序号
      part1?: any; // 序号1
      part2?: string; // 序号2
      part3?: string; // 序号3
    };
    normalPrice?: string; // 正常售价
    number?: string; // 编号
    collectPlatform?: string; // 采集平台
    collectTimestamp?: number; // 采集时间戳
    singleMixed?: string; // Single/Mixed
    // 用于存储任何其他动态字段
    [key: string]: any;
  };
  
  // 用于向后兼容和动态字段扩展
  dynamicFields?: {
    [key: string]: any;
  };
}

const ProductSchema = new Schema<IProduct>({
  productId: { type: String, required: true, unique: true, index: true },
  recordId: { type: String, required: true },
  name: { type: String, required: true, index: true },
  sequence: { type: String, required: true },
  
  category: {
    primary: { type: String, required: true, index: true },
    secondary: { type: String, required: false, default: '' }
  },
  
  price: {
    normal: { type: Number, required: true, index: true },
    discount: { type: Number, default: 0 },
    discountRate: { type: Number, default: 0 },
    currency: { type: String, default: 'CNY' },
    usd: { type: Number },
    specialUsd: { type: Number }
  },
  
  images: {
    front: String,
    back: String,
    label: String,
    package: String,
    gift: String
  },
  
  origin: {
    country: { type: String, default: '中国' },
    province: { type: String, index: true },
    city: String
  },
  
  platform: { type: String, required: true, index: true },
  specification: String,
  flavor: String,
  manufacturer: String,
  
  collectTime: { type: Date, required: true, index: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  
  searchText: { type: String, index: 'text' },
  status: { type: String, default: 'active', index: true },
  isVisible: { type: Boolean, default: true, index: true },

  // 新增字段支持飞书数据
  feishuData: {
    type: Schema.Types.Mixed,
    default: {}
  },
  
  // 动态字段扩展
  dynamicFields: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  strict: false // 允许存储未定义的字段
});

// 复合索引
ProductSchema.index({ status: 1, isVisible: 1, 'category.primary': 1, collectTime: -1 });
ProductSchema.index({ platform: 1, 'origin.province': 1 });
ProductSchema.index({ 'price.normal': 1, 'category.primary': 1 });

// 全文搜索索引
ProductSchema.index({ 
  name: 'text', 
  searchText: 'text', 
  manufacturer: 'text' 
}, {
  weights: { 
    name: 10, 
    searchText: 5, 
    manufacturer: 1 
  }
});

export const Product = mongoose.model<IProduct>('Product', ProductSchema);