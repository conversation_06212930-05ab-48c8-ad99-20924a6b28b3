require('dotenv').config();
const mongoose = require('mongoose');
const { Product } = require('./src/models/Product');

async function checkDatabaseProducts() {
  try {
    console.log('🔍 连接数据库...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');

    console.log('\n📊 检查数据库中的产品数据...');
    
    // 获取总产品数
    const totalProducts = await Product.countDocuments();
    console.log(`   - 总产品数: ${totalProducts}`);
    
    // 获取活跃产品数
    const activeProducts = await Product.countDocuments({ status: 'active' });
    console.log(`   - 活跃产品数: ${activeProducts}`);
    
    // 获取飞书来源的产品数
    const feishuProducts = await Product.countDocuments({ platform: 'Feishu' });
    console.log(`   - 飞书产品数: ${feishuProducts}`);
    
    // 获取最近的几个产品
    console.log('\n📋 最近的产品 (前5个):');
    const recentProducts = await Product.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('productId name platform category.primary collectTime');
    
    recentProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.productId} - ${product.name}`);
      console.log(`      平台: ${product.platform}, 分类: ${product.category.primary}`);
      console.log(`      采集时间: ${product.collectTime.toLocaleString()}`);
    });
    
    // 检查productId的分布
    console.log('\n🔑 产品ID前缀分布:');
    const productIds = await Product.find({}, 'productId').lean();
    const prefixCount = {};
    
    productIds.forEach(p => {
      const prefix = p.productId.substring(0, 3);
      prefixCount[prefix] = (prefixCount[prefix] || 0) + 1;
    });
    
    Object.entries(prefixCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([prefix, count]) => {
        console.log(`   - ${prefix}*: ${count} 个产品`);
      });
    
    // 检查是否有重复的productId
    console.log('\n🔍 检查重复的productId...');
    const duplicates = await Product.aggregate([
      { $group: { _id: '$productId', count: { $sum: 1 } } },
      { $match: { count: { $gt: 1 } } },
      { $limit: 10 }
    ]);
    
    if (duplicates.length > 0) {
      console.log(`   ⚠️ 发现 ${duplicates.length} 个重复的productId:`);
      duplicates.forEach(dup => {
        console.log(`     - ${dup._id}: ${dup.count} 个重复`);
      });
    } else {
      console.log('   ✅ 没有发现重复的productId');
    }
    
    console.log('\n✅ 数据库检查完成');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

checkDatabaseProducts();