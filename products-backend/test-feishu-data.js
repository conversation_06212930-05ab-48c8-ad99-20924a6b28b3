const axios = require('axios');
require('dotenv').config();

class FeishuDataFetcher {
  constructor() {
    this.config = {
      APP_ID: process.env.FEISHU_APP_ID,
      APP_SECRET: process.env.FEISHU_APP_SECRET,
      APP_TOKEN: process.env.FEISHU_APP_TOKEN,
      TABLE_ID: process.env.FEISHU_TABLE_ID,
      BASE_URL: process.env.FEISHU_BASE_URL || 'https://open.feishu.cn/open-apis/'
    };
    this.tenantAccessToken = null;
  }

  // 获取tenant_access_token
  async getTenantAccessToken() {
    try {
      console.log('正在获取飞书访问令牌...');
      console.log('APP_ID:', this.config.APP_ID ? '已配置' : '未配置');
      console.log('APP_SECRET:', this.config.APP_SECRET ? '已配置' : '未配置');
      
      const response = await axios.post(
        `${this.config.BASE_URL}auth/v3/tenant_access_token/internal`,
        {
          app_id: this.config.APP_ID,
          app_secret: this.config.APP_SECRET
        }
      );

      if (response.data.code === 0) {
        this.tenantAccessToken = response.data.tenant_access_token;
        console.log('✅ 飞书访问令牌获取成功');
        return true;
      } else {
        console.log('❌ 获取飞书访问令牌失败:', response.data.msg);
        return false;
      }
    } catch (error) {
      console.log('❌ 获取飞书访问令牌异常:', error.message);
      return false;
    }
  }

  // 获取应用信息
  async getAppInfo() {
    try {
      const url = `${this.config.BASE_URL}bitable/v1/apps/${this.config.APP_TOKEN}`;
      
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${this.tenantAccessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.code === 0) {
        console.log('✅ 应用信息获取成功:');
        console.log('   - 应用名称:', response.data.data.app.name);
        console.log('   - 应用ID:', response.data.data.app.app_token);
        console.log('   - 版本:', response.data.data.app.version);
        return response.data.data;
      } else {
        console.log('❌ 获取应用信息失败:', response.data.msg);
        return null;
      }
    } catch (error) {
      console.log('❌ 获取应用信息异常:', error.message);
      if (error.response) {
        console.log('   - 状态码:', error.response.status);
        console.log('   - 错误详情:', error.response.data);
      }
      return null;
    }
  }

  // 列出所有表格
  async listTables() {
    try {
      const url = `${this.config.BASE_URL}bitable/v1/apps/${this.config.APP_TOKEN}/tables`;
      
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${this.tenantAccessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.code === 0) {
        console.log('✅ 表格列表获取成功:');
        response.data.data.items.forEach((table, index) => {
          console.log(`   ${index + 1}. ${table.name} (ID: ${table.table_id})`);
        });
        return response.data.data.items;
      } else {
        console.log('❌ 获取表格列表失败:', response.data.msg);
        return null;
      }
    } catch (error) {
      console.log('❌ 获取表格列表异常:', error.message);
      return null;
    }
  }

  // 获取表格信息
  async getTableInfo(tableId = null) {
    try {
      const targetTableId = tableId || this.config.TABLE_ID;
      const url = `${this.config.BASE_URL}bitable/v1/apps/${this.config.APP_TOKEN}/tables/${targetTableId}`;
      
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${this.tenantAccessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.code === 0) {
        console.log('✅ 表格信息获取成功:');
        console.log('   - 表格名称:', response.data.data.table.name);
        console.log('   - 表格ID:', response.data.data.table.table_id);
        return response.data.data;
      } else {
        console.log('❌ 获取表格信息失败:', response.data.msg);
        return null;
      }
    } catch (error) {
      console.log('❌ 获取表格信息异常:', error.message);
      if (error.response) {
        console.log('   - 状态码:', error.response.status);
        console.log('   - 错误详情:', error.response.data);
      }
      return null;
    }
  }
  // 获取记录数据
  async getRecords(tableId = null, pageSize = 100, pageToken = null) {
    try {
      const targetTableId = tableId || this.config.TABLE_ID;
      const url = `${this.config.BASE_URL}bitable/v1/apps/${this.config.APP_TOKEN}/tables/${targetTableId}/records`;
      
      const params = {
        page_size: pageSize
      };
      
      if (pageToken) {
        params.page_token = pageToken;
      }

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${this.tenantAccessToken}`,
          'Content-Type': 'application/json'
        },
        params
      });

      if (response.data.code === 0) {
        return {
          success: true,
          data: response.data.data
        };
      } else {
        console.log('❌ 获取记录失败:', response.data.msg);
        return {
          success: false,
          error: response.data.msg
        };
      }
    } catch (error) {
      console.log('❌ 获取记录异常:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取所有记录
  async getAllRecords(tableId = null) {
    const allRecords = [];
    let pageToken = null;
    let totalPages = 0;

    do {
      totalPages++;
      console.log(`正在获取第 ${totalPages} 页数据...`);
      
      const result = await this.getRecords(tableId, 500, pageToken);
      if (!result.success) {
        console.log('获取数据失败:', result.error);
        break;
      }

      const { items, has_more, page_token } = result.data;
      allRecords.push(...items);
      pageToken = has_more ? page_token : null;

      console.log(`   - 本页获取 ${items.length} 条记录`);
      
      // 避免请求过于频繁
      if (pageToken) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } while (pageToken);

    return allRecords;
  }

  // 分析数据结构
  analyzeDataStructure(records) {
    if (records.length === 0) {
      console.log('❌ 没有记录可供分析');
      return;
    }

    console.log('\n📊 数据结构分析:');
    console.log('==========================================');
    
    const sampleRecord = records[0];
    console.log('示例记录结构:');
    console.log('- record_id:', sampleRecord.record_id);
    console.log('- created_time:', new Date(sampleRecord.created_time));
    console.log('- last_modified_time:', new Date(sampleRecord.last_modified_time));
    
    console.log('\n字段列表:');
    const fields = sampleRecord.fields;
    const fieldNames = Object.keys(fields);
    fieldNames.forEach((name, index) => {
      const value = fields[name];
      const type = Array.isArray(value) ? 'array' : typeof value;
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${name}: ${type}`);
      
      // 显示前几个值的样例
      if (Array.isArray(value) && value.length > 0) {
        console.log(`    样例: [${value.slice(0, 2).map(v => typeof v === 'object' ? JSON.stringify(v).slice(0, 50) + '...' : v).join(', ')}]`);
      } else if (typeof value === 'object' && value !== null) {
        console.log(`    样例: ${JSON.stringify(value).slice(0, 100)}...`);
      } else {
        console.log(`    样例: ${String(value).slice(0, 50)}`);
      }
    });

    // 统计信息
    console.log('\n📈 统计信息:');
    console.log(`总记录数: ${records.length}`);
    console.log(`字段数量: ${fieldNames.length}`);
    
    // 分析时间分布
    const times = records.map(r => new Date(r.last_modified_time));
    const latestTime = new Date(Math.max(...times));
    const earliestTime = new Date(Math.min(...times));
    console.log(`最新修改时间: ${latestTime.toLocaleString()}`);
    console.log(`最早修改时间: ${earliestTime.toLocaleString()}`);
  }

  // 展示数据样例
  showDataSamples(records, count = 5) {
    console.log(`\n📝 数据样例 (前${count}条):`);
    console.log('==========================================');
    
    records.slice(0, count).forEach((record, index) => {
      console.log(`\n记录 ${index + 1}:`);
      console.log(`ID: ${record.record_id}`);
      console.log(`修改时间: ${new Date(record.last_modified_time).toLocaleString()}`);
      
      // 尝试显示关键字段
      const fields = record.fields;
      const keyFields = ['产品名称', 'name', '产品ID', 'id', '一级分类', 'category', '价格', 'price'];
      
      keyFields.forEach(key => {
        if (fields[key] !== undefined) {
          let value = fields[key];
          if (typeof value === 'object') {
            value = JSON.stringify(value).slice(0, 100);
          }
          console.log(`${key}: ${value}`);
        }
      });
    });
  }
}

// 主函数
async function main() {
  console.log('🚀 开始获取飞书数据...\n');
  
  const fetcher = new FeishuDataFetcher();
  
  // 检查配置
  console.log('配置检查:');
  console.log('- APP_TOKEN:', fetcher.config.APP_TOKEN ? '已配置' : '❌ 未配置');
  console.log('- TABLE_ID:', fetcher.config.TABLE_ID ? '已配置' : '❌ 未配置');
  console.log('- BASE_URL:', fetcher.config.BASE_URL);
  console.log('');

  // 获取访问令牌
  const tokenSuccess = await fetcher.getTenantAccessToken();
  if (!tokenSuccess) {
    console.log('❌ 无法获取访问令牌，终止操作');
    return;
  }

  // 获取应用信息
  console.log('\n📱 获取应用信息...');
  const appInfo = await fetcher.getAppInfo();
  if (!appInfo) {
    console.log('❌ 无法获取应用信息，终止操作');
    return;
  }

  // 列出所有表格
  console.log('\n📋 获取表格列表...');
  const tables = await fetcher.listTables();
  if (!tables || tables.length === 0) {
    console.log('❌ 无法获取表格列表，终止操作');
    return;
  }

  // 尝试第一个表格
  const firstTable = tables[0];
  console.log(`\n🔍 使用第一个表格: ${firstTable.name} (${firstTable.table_id})`);

  // 获取表格信息 (跳过，直接获取数据)
  console.log('\n📊 跳过表格信息，直接获取数据记录...');

  // 获取所有记录
  const records = await fetcher.getAllRecords(firstTable.table_id);
  
  if (records.length === 0) {
    console.log('❌ 没有获取到任何数据');
    return;
  }

  console.log(`\n✅ 成功获取 ${records.length} 条记录！`);

  // 分析数据结构
  fetcher.analyzeDataStructure(records);

  // 展示数据样例
  fetcher.showDataSamples(records);

  console.log('\n🎉 飞书数据获取完成！');
}

// 运行主函数
main().catch(error => {
  console.error('❌ 执行失败:', error.message);
});