require('dotenv').config();
const mongoose = require('mongoose');
const { feishuService } = require('./src/services/feishuService');
const { Product } = require('./src/models/Product');

async function testDirectSync() {
  try {
    console.log('🚀 开始直接飞书同步测试...\n');
    
    // 连接数据库
    console.log('🔍 连接数据库...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');

    // 测试飞书连接
    console.log('\n🔗 测试飞书连接...');
    const connectionTest = await feishuService.testConnection();
    console.log(`连接状态: ${connectionTest.success ? '✅ 成功' : '❌ 失败'}`);
    if (!connectionTest.success) {
      console.log('错误:', connectionTest.message);
      return;
    }

    // 获取飞书数据
    console.log('\n📊 获取飞书数据...');
    const records = await feishuService.getAllRecords();
    console.log(`获取到 ${records.length} 条记录`);

    if (records.length === 0) {
      console.log('❌ 没有获取到飞书数据');
      return;
    }

    // 转换数据
    console.log('\n🔄 转换数据格式...');
    const products = feishuService.transformRecordsToProducts(records);
    console.log(`转换了 ${products.length} 个产品`);

    // 显示前几个产品的信息
    console.log('\n📋 转换后的产品样例 (前3个):');
    products.slice(0, 3).forEach((product, index) => {
      console.log(`\n产品 ${index + 1}:`);
      console.log(`   - ID: ${product.id}`);
      console.log(`   - 名称: ${product.name}`);
      console.log(`   - 分类: ${product.category?.primary}`);
      console.log(`   - 平台: ${product.platform}`);
      console.log(`   - 价格: ${product.price?.normal}`);
    });

    // 检查数据库中是否已存在
    console.log('\n🔍 检查数据库中的现有产品...');
    const existingIds = products.map(p => p.id);
    const existingProducts = await Product.find({ 
      productId: { $in: existingIds } 
    }).select('productId');
    
    console.log(`飞书产品ID数量: ${existingIds.length}`);
    console.log(`数据库中已存在: ${existingProducts.length} 个`);

    const existingMap = new Map(existingProducts.map(p => [p.productId, p]));
    const newProducts = products.filter(p => !existingMap.has(p.id));
    
    console.log(`需要创建的新产品: ${newProducts.length} 个`);

    // 尝试创建前5个新产品作为测试
    if (newProducts.length > 0) {
      console.log('\n💾 测试创建前5个新产品...');
      const testProducts = newProducts.slice(0, 5);
      
      let created = 0;
      let errors = 0;
      
      for (const product of testProducts) {
        try {
          // 转换为数据库格式
          const dbProduct = {
            productId: product.id,
            recordId: product.recordId,
            name: product.name || '未命名产品',
            sequence: product.sequence || '',
            category: {
              primary: product.category?.primary || '未分类',
              secondary: product.category?.secondary || ''
            },
            price: {
              normal: parseFloat(product.price?.normal) || 0,
              discount: parseFloat(product.price?.discount) || 0,
              discountRate: 0,
              currency: 'CNY',
              usd: product.price?.usd,
              specialUsd: product.price?.specialUsd
            },
            images: product.images || {},
            origin: {
              country: product.origin?.country || '中国',
              province: product.origin?.province || '',
              city: product.origin?.city || ''
            },
            platform: product.platform || 'Feishu',
            specification: product.specification || '',
            flavor: product.flavor || '',
            manufacturer: product.manufacturer || '',
            collectTime: new Date(product.collectTime || Date.now()),
            searchText: `${product.name} ${product.category?.primary} ${product.platform}`.toLowerCase(),
            status: 'active',
            isVisible: true,
            feishuData: product.feishuData || {},
            dynamicFields: product.dynamicFields || {}
          };

          const newProduct = await Product.create(dbProduct);
          console.log(`   ✅ 创建成功: ${newProduct.productId} - ${newProduct.name}`);
          created++;
          
        } catch (error) {
          console.log(`   ❌ 创建失败: ${product.id} - ${error.message}`);
          errors++;
        }
      }
      
      console.log(`\n📊 测试结果: 成功创建 ${created} 个，失败 ${errors} 个`);
      
      // 验证创建结果
      const feishuProductsInDb = await Product.countDocuments({ platform: 'Feishu' });
      console.log(`\n🔍 数据库中飞书产品总数: ${feishuProductsInDb}`);
    }

    console.log('\n✅ 直接同步测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('堆栈:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

testDirectSync();