require('dotenv').config();
const { FeishuApiUtils } = require('./src/config/feishuConfig');

// 模拟一条飞书记录数据（基于我们之前获取的实际结构）
const mockFeishuRecord = {
  record_id: "rectq2ENo8",
  created_time: 1720000000000,
  last_modified_time: 1720000000000,
  fields: {
    "Back image(背)": [{"file_token":"VKy3bgddSo1aDdxY1aXcZ90Qnkf","name":"back.jpg","url":"https://example.com/back.jpg"}],
    "Category Level 2": "Yogurt/Lactic acid bacteria",
    "Client(委托方)": "卡士乳业（深圳）有限公司",
    "Flavor(口味)": "Lemon and sweet orange flavor",
    "Front image(正)": [{"file_token":"KoMHb0FfAoMhChxlA0ccL6EGnqb","name":"front.jpg","url":"https://example.com/front.jpg"}],
    "Manufacturer(生产商)": "卡士酸奶（苏州）有限公司",
    "Origin (City)": ["Suzhou苏州"],
    "Origin (Country)": "China",
    "Origin (Province)": ["Jiangsu"],
    "Price（USD）": 1.82857142857143,
    "Product Name": "Cowala 007 Probiotic Yogurt",
    "Single/Mixed": "Single",
    "Special Price（USD）": 1.46285714285714,
    "Specs(规格)": "440g",
    "Tag photo(标签)": [{"file_token":"O4UObOHBOoactZxBqITcznAZnwd","name":"label.jpg","url":"https://example.com/label.jpg"}],
    "bar code(条码)": "6924810802859",
    "rx编号": "RX00000001",
    "产品品名": [{"text":"test product","type":"text"}],
    "优惠到手价": "10.24",
    "口味": "柠檬甜橙味",
    "品名": "卡士007益生菌酸奶",
    "品类一级": "乳品烘焙",
    "品类二级": "酸奶/乳酸菌",
    "序号": [{"text":"HM-0001A","type":"text"}],
    "序号1": [{"text":"HM","type":"text"}],
    "序号2": "0001",
    "序号3": "A",
    "正常售价": "12.8",
    "编号": "20250708-002",
    "采集平台": "盒马APP",
    "采集时间": 1752806356000
  }
};

console.log('🧪 测试飞书字段映射逻辑...\n');

try {
  // 测试字段映射
  const mappedData = FeishuApiUtils.mapFeishuFields(mockFeishuRecord.fields);
  
  console.log('✅ 字段映射成功！');
  console.log('\n📋 映射结果:');
  console.log('==========================================');
  
  console.log('🔍 基础信息:');
  console.log(`   - ID: ${mappedData.id}`);
  console.log(`   - 名称: ${mappedData.name}`);
  console.log(`   - 序号: ${mappedData.sequence}`);
  
  console.log('\n📂 分类信息:');
  console.log(`   - 一级分类: ${mappedData.category.primary}`);
  console.log(`   - 二级分类: ${mappedData.category.secondary}`);
  
  console.log('\n💰 价格信息:');
  console.log(`   - 正常价格: ${mappedData.price.normal}`);
  console.log(`   - 折扣价格: ${mappedData.price.discount}`);
  console.log(`   - USD价格: ${mappedData.price.usd}`);
  console.log(`   - 特价USD: ${mappedData.price.specialUsd}`);
  
  console.log('\n🌏 产地信息:');
  console.log(`   - 国家: ${mappedData.origin.country}`);
  console.log(`   - 省份: ${mappedData.origin.province}`);
  console.log(`   - 城市: ${mappedData.origin.city}`);
  
  console.log('\n🖼️ 图片信息:');
  console.log(`   - 正面图: ${mappedData.images.front}`);
  console.log(`   - 背面图: ${mappedData.images.back}`);
  console.log(`   - 标签图: ${mappedData.images.label}`);
  
  console.log('\n📦 其他信息:');
  console.log(`   - 平台: ${mappedData.platform}`);
  console.log(`   - 规格: ${mappedData.specification}`);
  console.log(`   - 口味: ${mappedData.flavor}`);
  console.log(`   - 生产商: ${mappedData.manufacturer}`);
  
  console.log('\n🏷️ 飞书特有数据:');
  console.log(`   - 委托方: ${mappedData.feishuData?.client}`);
  console.log(`   - 条码: ${mappedData.feishuData?.barcode}`);
  console.log(`   - RX编号: ${mappedData.feishuData?.rxNumber}`);
  console.log(`   - 编号: ${mappedData.feishuData?.number}`);
  console.log(`   - 采集平台: ${mappedData.feishuData?.collectPlatform}`);
  
  console.log('\n📊 动态字段数量:', Object.keys(mappedData.dynamicFields || {}).length);
  
  console.log('\n⏰ 时间信息:');
  console.log(`   - 采集时间: ${new Date(mappedData.collectTime).toLocaleString()}`);
  
  // 验证必需字段
  console.log('\n✅ 验证必需字段:');
  const requiredFields = ['id', 'name', 'category', 'price'];
  let allValid = true;
  
  requiredFields.forEach(field => {
    const hasField = mappedData[field] !== undefined && mappedData[field] !== null && mappedData[field] !== '';
    console.log(`   - ${field}: ${hasField ? '✅' : '❌'}`);
    if (!hasField) allValid = false;
  });
  
  console.log(`\n${allValid ? '🎉' : '⚠️'} 数据验证: ${allValid ? '通过' : '失败'}`);
  
  // 显示完整的原始数据结构
  console.log('\n📝 飞书原始字段:');
  Object.keys(mockFeishuRecord.fields).forEach((key, index) => {
    const value = mockFeishuRecord.fields[key];
    const type = Array.isArray(value) ? 'array' : typeof value;
    console.log(`   ${(index + 1).toString().padStart(2, ' ')}. ${key}: ${type}`);
  });
  
} catch (error) {
  console.error('❌ 字段映射失败:', error.message);
  console.error('   堆栈:', error.stack);
}

console.log('\n🧪 测试完成！');