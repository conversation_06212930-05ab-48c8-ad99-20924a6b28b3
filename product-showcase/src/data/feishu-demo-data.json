[{"id": "feishu-001", "recordId": "recFeishu001", "name": "飞书同步测试产品1", "sequence": "FS-", "category": {"primary": "飞书测试分类", "secondary": "API同步测试"}, "price": {"normal": 59.9, "discount": 49.9, "discountRate": 17}, "images": {"front": "feishu/feishu-product-001_front.jpg", "back": "feishu/feishu-product-001_back.jpg", "label": "feishu/feishu-product-001_label.jpg"}, "origin": {"country": "中国", "province": "广东", "city": "深圳"}, "platform": "飞书多维表格", "specification": "800g", "flavor": "蜂蜜柠檬味", "manufacturer": "飞书测试制造商", "collectTime": "2025-07-20T10:30:00.000Z"}, {"id": "feishu-002", "recordId": "recFeishu002", "name": "飞书同步测试产品2", "sequence": "FS-", "category": {"primary": "飞书测试分类", "secondary": "API同步测试"}, "price": {"normal": 32.8, "discount": 0, "discountRate": 0}, "images": {"front": "feishu/feishu-product-002_front.jpg", "label": "feishu/feishu-product-002_label.jpg"}, "origin": {"country": "中国", "province": "浙江", "city": "杭州"}, "platform": "飞书多维表格", "specification": "450ml", "flavor": "原味", "manufacturer": "飞书测试制造商2", "collectTime": "2025-07-20T10:31:00.000Z"}, {"id": "feishu-003", "recordId": "recFeishu003", "name": "飞书同步测试产品3", "sequence": "FS-", "category": {"primary": "飞书测试分类", "secondary": "实时同步"}, "price": {"normal": 78.5, "discount": 68.5, "discountRate": 13}, "images": {"front": "feishu/feishu-product-003_front.jpg", "back": "feishu/feishu-product-003_back.jpg", "label": "feishu/feishu-product-003_label.jpg", "package": "feishu/feishu-product-003_package.jpg"}, "origin": {"country": "中国", "province": "四川", "city": "成都"}, "platform": "飞书多维表格", "specification": "1.2kg", "flavor": "麻辣味", "manufacturer": "飞书测试制造商3", "collectTime": "2025-07-20T10:32:00.000Z"}]