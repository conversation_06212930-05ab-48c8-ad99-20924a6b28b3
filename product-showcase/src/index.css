@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义基础样式 */
body {
  background-color: #f9fafb;
  color: #111827;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* 自定义组件样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: colors 0.2s;
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #111827;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-ghost:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.card {
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.input::placeholder {
  color: #6b7280;
}

.input:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 自定义工具类 */
.text-balance {
  text-wrap: balance;
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f8fafc;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.scrollbar-thin::-webkit-scrollbar-thumb:active {
  background: #64748b;
}

/* 产品详情面板滚动优化 */
.product-detail-scroll {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* 确保flex容器正确处理溢出 */
.flex-scroll-container {
  min-height: 0;
  flex: 1;
}

/* 针对移动设备的滚动优化 */
@media (max-width: 1024px) {
  .product-detail-scroll {
    -webkit-overflow-scrolling: touch;
  }
}
